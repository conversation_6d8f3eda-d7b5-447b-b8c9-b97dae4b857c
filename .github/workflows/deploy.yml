name: Deploy AstroKabinet with Rollback

on:
  push:
    branches:
      - main
      - production
  workflow_dispatch:
    inputs:
      rollback:
        description: 'Rollback to previous version'
        required: false
        default: 'false'
        type: boolean

env:
  DEPLOY_PATH: /home/<USER>/astrokabinet
  BACKUP_PATH: /home/<USER>/backups
  DOCKER_COMPOSE_FILE: docker-compose.production.yml

jobs:
  # Job 1: Run Tests
  test:
    runs-on: ubuntu-latest
    if: github.event.inputs.rollback != 'true'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, redis
          coverage: none

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: vendor
          key: composer-${{ hashFiles('composer.lock') }}

      - name: Install Composer Dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install NPM Dependencies
        run: npm ci

      - name: Build Assets
        run: npm run build

      - name: Create .env for testing
        run: |
          cp .env.example .env
          php artisan key:generate

      - name: Run Tests
        run: php artisan test --parallel

  # Job 2: Deploy to Production
  deploy:
    runs-on: ubuntu-latest
    needs: test
    if: always() && (needs.test.result == 'success' || github.event.inputs.rollback == 'true')

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        if: github.event.inputs.rollback != 'true'

      - name: Setup deployment environment
        if: github.event.inputs.rollback != 'true'
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, redis

      - name: Install and build assets
        if: github.event.inputs.rollback != 'true'
        run: |
          composer install --no-dev --no-interaction --prefer-dist --optimize-autoloader
          npm ci
          npm run build

      # Backup current deployment
      - name: Create backup of current deployment
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            # Create backup directory
            mkdir -p ${{ env.BACKUP_PATH }}

            # Create backup with timestamp
            BACKUP_NAME="astrokabinet-$(date +%Y%m%d-%H%M%S)"

            if [ -d "${{ env.DEPLOY_PATH }}" ]; then
              echo "Creating backup: $BACKUP_NAME"
              cp -r ${{ env.DEPLOY_PATH }} ${{ env.BACKUP_PATH }}/$BACKUP_NAME

              # Keep only last 5 backups
              cd ${{ env.BACKUP_PATH }}
              ls -t | tail -n +6 | xargs -r rm -rf

              echo "BACKUP_NAME=$BACKUP_NAME" >> $GITHUB_ENV
            fi

      # Handle rollback
      - name: Rollback to previous version
        if: github.event.inputs.rollback == 'true'
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd ${{ env.BACKUP_PATH }}
            LATEST_BACKUP=$(ls -t | head -n 1)

            if [ -n "$LATEST_BACKUP" ]; then
              echo "Rolling back to: $LATEST_BACKUP"

              # Stop current containers
              cd ${{ env.DEPLOY_PATH }}
              docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} down || true

              # Restore from backup
              rm -rf ${{ env.DEPLOY_PATH }}
              cp -r ${{ env.BACKUP_PATH }}/$LATEST_BACKUP ${{ env.DEPLOY_PATH }}

              # Start containers
              cd ${{ env.DEPLOY_PATH }}
              docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} up -d

              echo "Rollback completed successfully"
            else
              echo "No backup found for rollback"
              exit 1
            fi

      # Deploy new version
      - name: Deploy to server
        if: github.event.inputs.rollback != 'true'
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "."
          target: "${{ env.DEPLOY_PATH }}"
          overwrite: true

      # Deploy and health check
      - name: Deploy and verify
        if: github.event.inputs.rollback != 'true'
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd ${{ env.DEPLOY_PATH }}

            # Set proper permissions
            sudo chown -R deploy:deploy .

            # Create .env if not exists
            if [ ! -f .env ]; then
              cp .env.example .env
              echo "Created .env from example"
            fi

            # Build and start containers with retry mechanism
            for i in {1..3}; do
              echo "Deployment attempt $i/3"

              # Stop existing containers
              docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} down || true

              # Build with no cache to ensure fresh build
              if docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} build --no-cache; then
                echo "Build successful"
                break
              else
                echo "Build failed on attempt $i"
                if [ $i -eq 3 ]; then
                  echo "All build attempts failed, initiating rollback"
                  exit 1
                fi
                sleep 10
              fi
            done

            # Start containers
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} up -d

            # Wait for containers to be ready
            echo "Waiting for containers to start..."
            sleep 30

            # Health check with retry
            for i in {1..10}; do
              if curl -f http://localhost/up > /dev/null 2>&1; then
                echo "Health check passed"
                break
              else
                echo "Health check failed, attempt $i/10"
                if [ $i -eq 10 ]; then
                  echo "Health check failed after 10 attempts, initiating rollback"
                  exit 1
                fi
                sleep 10
              fi
            done

            # Run Laravel optimizations
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} exec -T app php artisan config:cache
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} exec -T app php artisan route:cache
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} exec -T app php artisan view:cache

            echo "Deployment completed successfully"

      # Rollback on failure
      - name: Rollback on deployment failure
        if: failure() && github.event.inputs.rollback != 'true'
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            echo "Deployment failed, initiating automatic rollback"

            cd ${{ env.BACKUP_PATH }}
            LATEST_BACKUP=$(ls -t | head -n 1)

            if [ -n "$LATEST_BACKUP" ]; then
              echo "Rolling back to: $LATEST_BACKUP"

              # Stop failed deployment
              cd ${{ env.DEPLOY_PATH }}
              docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} down || true

              # Restore from backup
              rm -rf ${{ env.DEPLOY_PATH }}
              cp -r ${{ env.BACKUP_PATH }}/$LATEST_BACKUP ${{ env.DEPLOY_PATH }}

              # Start containers
              cd ${{ env.DEPLOY_PATH }}
              docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} up -d

              echo "Automatic rollback completed"
            else
              echo "No backup available for rollback"
            fi

      # Notify deployment status
      - name: Notify deployment result
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Deployment successful"
          else
            echo "❌ Deployment failed and rolled back"
          fi
