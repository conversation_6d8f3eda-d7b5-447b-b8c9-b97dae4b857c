name: Deploy AstroKabinet with Git Rollback

on:
  push:
    branches:
      - main
      - production
  workflow_dispatch:
    inputs:
      rollback:
        description: 'Rollback to previous production tag'
        required: false
        default: 'false'
        type: boolean
      rollback_tag:
        description: 'Specific tag to rollback to (optional)'
        required: false
        type: string

env:
  DEPLOY_PATH: /var/www/astrokabinet.id
  DOCKER_COMPOSE_FILE: docker-compose.production.yml

jobs:
  # Job 1: Run Tests
  test:
    runs-on: ubuntu-latest
    if: github.event.inputs.rollback != 'true'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, redis
          coverage: none

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: vendor
          key: composer-${{ hashFiles('composer.lock') }}

      - name: Install Composer Dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install NPM Dependencies
        run: npm ci

      - name: Build Assets
        run: npm run build

      - name: Create .env for testing
        run: |
          cp .env.example .env
          php artisan key:generate

      - name: Run Tests
        run: php artisan test --parallel

  # Job 2: Deploy to Production
  deploy:
    runs-on: ubuntu-latest
    needs: test
    permissions:
      contents: write
    if: always() && (needs.test.result == 'success' || github.event.inputs.rollback == 'true')

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Need full history for Git operations

      - name: Setup deployment environment
        if: github.event.inputs.rollback != 'true'
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, redis

      - name: Install and build assets
        if: github.event.inputs.rollback != 'true'
        run: |
          composer install --no-dev --no-interaction --prefer-dist --optimize-autoloader
          npm ci
          npm run build

      # Create production tag for successful deployments
      - name: Create production tag
        if: github.event.inputs.rollback != 'true'
        run: |
          TIMESTAMP=$(date +%Y%m%d-%H%M%S)
          TAG_NAME="production-${TIMESTAMP}"
          echo "PRODUCTION_TAG=${TAG_NAME}" >> $GITHUB_ENV
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"
          git tag -a "${TAG_NAME}" -m "Production deployment ${TIMESTAMP}"
          git push origin "${TAG_NAME}"

          # Clean up old production tags (keep only last 10)
          OLD_TAGS=$(git tag -l "production-*" | sort -V | head -n -10)
          if [ -n "$OLD_TAGS" ]; then
            echo "Cleaning up old production tags:"
            echo "$OLD_TAGS"
            echo "$OLD_TAGS" | xargs git tag -d
            echo "$OLD_TAGS" | xargs git push --delete origin
          fi

      # Handle Git-based rollback
      - name: Git rollback to previous production tag
        if: github.event.inputs.rollback == 'true'
        run: |
          # Determine which tag to rollback to
          if [ -n "${{ github.event.inputs.rollback_tag }}" ]; then
            ROLLBACK_TAG="${{ github.event.inputs.rollback_tag }}"
            echo "Rolling back to specified tag: $ROLLBACK_TAG"
          else
            # Get the latest production tag (excluding current if exists)
            ROLLBACK_TAG=$(git tag -l "production-*" | sort -V | tail -n 2 | head -n 1)
            echo "Rolling back to latest production tag: $ROLLBACK_TAG"
          fi

          if [ -z "$ROLLBACK_TAG" ]; then
            echo "No production tag found for rollback"
            exit 1
          fi

          # Checkout the rollback tag
          git checkout "$ROLLBACK_TAG"
          echo "ROLLBACK_TAG=$ROLLBACK_TAG" >> $GITHUB_ENV

          # Build assets for rollback version
          composer install --no-dev --no-interaction --prefer-dist --optimize-autoloader
          npm ci
          npm run build

          # Create .deployignore for rollback
          cat > .deployignore << 'EOF'
          .git/
          .github/
          node_modules/
          tests/
          storage/logs/
          storage/framework/cache/
          storage/framework/sessions/
          storage/framework/views/
          .env
          .env.example
          .gitignore
          .deployignore
          README.md
          *.log
          .DS_Store
          Thumbs.db
          EOF

      # NEW STEP: Create deployment directory and set permissions
      - name: Create deployment directory
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            # Create deployment directory (assuming user has permission or directory exists)
            mkdir -p ${{ env.DEPLOY_PATH }} || echo "Directory creation failed, assuming it exists"

            # Try to set ownership if we have permission, otherwise continue
            chown -R ${{ secrets.VPS_USERNAME }}:${{ secrets.VPS_USERNAME }} ${{ env.DEPLOY_PATH }} 2>/dev/null || echo "Could not change ownership, continuing..."

      # Create deployment archive (exclude unnecessary files)
      - name: Create deployment archive
        run: |
          # Create .deployignore to exclude unnecessary files
          cat > .deployignore << 'EOF'
          .git/
          .github/
          node_modules/
          tests/
          storage/logs/
          storage/framework/cache/
          storage/framework/sessions/
          storage/framework/views/
          .env
          .env.example
          .gitignore
          .deployignore
          README.md
          *.log
          .DS_Store
          Thumbs.db
          EOF

          # Create deployment archive
          tar --exclude-from=.deployignore -czf deployment.tar.gz .
          ls -lh deployment.tar.gz

      # Deploy to server (works for both new deployments and rollbacks)
      - name: Deploy to server
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "deployment.tar.gz"
          target: "/tmp/"
          overwrite: true

      # Extract and deploy files
      - name: Extract and deploy files
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            # Stop containers before deployment
            cd ${{ env.DEPLOY_PATH }} && docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} down || true

            # Backup current .env if exists
            if [ -f "${{ env.DEPLOY_PATH }}/.env" ]; then
              cp "${{ env.DEPLOY_PATH }}/.env" "/tmp/.env.backup"
              echo "Backed up existing .env file"
            fi

            # Extract new deployment
            cd ${{ env.DEPLOY_PATH }}
            tar -xzf /tmp/deployment.tar.gz

            # Restore .env if backup exists
            if [ -f "/tmp/.env.backup" ]; then
              cp "/tmp/.env.backup" ".env"
              echo "Restored .env file"
            fi

            # Set proper permissions
            sudo chown -R ${{ secrets.VPS_USERNAME }}:${{ secrets.VPS_USERNAME }} .

            # Clean up
            rm -f /tmp/deployment.tar.gz /tmp/.env.backup

      # Deploy and health check (works for both new deployments and rollbacks)
      - name: Deploy and verify
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd ${{ env.DEPLOY_PATH }}

            # Create .env if not exists
            if [ ! -f .env ]; then
              cp .env.example .env
              php artisan key:generate
              echo "Created .env from example"
            fi

            # Build and start containers with retry mechanism
            for i in {1..3}; do
              echo "Deployment attempt $i/3"

              # Stop existing containers
              docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} down || true

              # Build with no cache to ensure fresh build
              if docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} build --no-cache; then
                echo "Build successful"
                break
              else
                echo "Build failed on attempt $i"
                if [ $i -eq 3 ]; then
                  echo "All build attempts failed"
                  exit 1
                fi
                sleep 10
              fi
            done

            # Start containers
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} up -d

            # Wait for containers to be ready
            echo "Waiting for containers to start..."
            sleep 30

            # Health check with retry
            for i in {1..10}; do
              if curl -f http://localhost/up > /dev/null 2>&1; then
                echo "Health check passed"
                break
              else
                echo "Health check failed, attempt $i/10"
                if [ $i -eq 10 ]; then
                  echo "Health check failed after 10 attempts"
                  exit 1
                fi
                sleep 10
              fi
            done

            # Run Laravel optimizations
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} exec -T app php artisan config:cache
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} exec -T app php artisan route:cache
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} exec -T app php artisan view:cache

            echo "Deployment completed successfully"

      # Git-based rollback on failure
      - name: Git rollback on deployment failure
        if: failure() && github.event.inputs.rollback != 'true'
        run: |
          echo "Deployment failed, initiating automatic Git rollback"

          # Get the previous production tag for rollback
          PREVIOUS_TAG=$(git tag -l "production-*" | sort -V | tail -n 2 | head -n 1)

          if [ -n "$PREVIOUS_TAG" ]; then
            echo "Rolling back to previous production tag: $PREVIOUS_TAG"
            git checkout "$PREVIOUS_TAG"

            # Rebuild assets for rollback version
            composer install --no-dev --no-interaction --prefer-dist --optimize-autoloader
            npm ci
            npm run build

            # Create .deployignore for rollback
            cat > .deployignore << 'EOF'
          .git/
          .github/
          node_modules/
          tests/
          storage/logs/
          storage/framework/cache/
          storage/framework/sessions/
          storage/framework/views/
          .env
          .env.example
          .gitignore
          .deployignore
          README.md
          *.log
          .DS_Store
          Thumbs.db
          EOF

            echo "ROLLBACK_TAG=$PREVIOUS_TAG" >> $GITHUB_ENV
          else
            echo "No previous production tag found for automatic rollback"
            exit 1
          fi

      # Create rollback deployment archive
      - name: Create rollback deployment archive
        if: failure() && github.event.inputs.rollback != 'true' && env.ROLLBACK_TAG != ''
        run: |
          # Create deployment archive for rollback
          tar --exclude-from=.deployignore -czf rollback-deployment.tar.gz .

      # Deploy rollback version on failure
      - name: Deploy rollback version
        if: failure() && github.event.inputs.rollback != 'true' && env.ROLLBACK_TAG != ''
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "rollback-deployment.tar.gz"
          target: "/tmp/"
          overwrite: true

      # Extract rollback files
      - name: Extract rollback files
        if: failure() && github.event.inputs.rollback != 'true' && env.ROLLBACK_TAG != ''
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            # Backup current .env
            if [ -f "${{ env.DEPLOY_PATH }}/.env" ]; then
              cp "${{ env.DEPLOY_PATH }}/.env" "/tmp/.env.backup"
            fi

            # Extract rollback deployment
            cd ${{ env.DEPLOY_PATH }}
            tar -xzf /tmp/rollback-deployment.tar.gz

            # Restore .env
            if [ -f "/tmp/.env.backup" ]; then
              cp "/tmp/.env.backup" ".env"
            fi

            # Set permissions
            sudo chown -R ${{ secrets.VPS_USERNAME }}:${{ secrets.VPS_USERNAME }} .

            # Clean up
            rm -f /tmp/rollback-deployment.tar.gz /tmp/.env.backup

      # Restart services after rollback
      - name: Restart services after rollback
        if: failure() && github.event.inputs.rollback != 'true' && env.ROLLBACK_TAG != ''
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          port: ${{ secrets.VPS_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd ${{ env.DEPLOY_PATH }}
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} down || true
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} up -d
            echo "Automatic rollback to ${{ env.ROLLBACK_TAG }} completed"

      # Notify deployment status
      - name: Notify deployment result
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            if [ "${{ github.event.inputs.rollback }}" == "true" ]; then
              echo "✅ Rollback to ${{ env.ROLLBACK_TAG || 'previous version' }} successful"
            else
              echo "✅ Deployment to ${{ env.PRODUCTION_TAG }} successful"
            fi
          else
            echo "❌ Deployment failed"
            if [ -n "${{ env.ROLLBACK_TAG }}" ]; then
              echo "🔄 Automatic rollback to ${{ env.ROLLBACK_TAG }} attempted"
            fi
          fi
